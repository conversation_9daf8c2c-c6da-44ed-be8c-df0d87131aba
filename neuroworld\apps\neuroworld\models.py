from django.db import models
from django.db.models import UniqueConstraint

from django.contrib.auth.models import Abstract<PERSON><PERSON>User, BaseUserManager, PermissionsMixin
import uuid

import pytz

AUTH_PROVIDERS = [
        ('GOOGLE', 'google'),
        ('APPLE', 'apple'),
        ('BASE', 'base')
    ]

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        return self.create_user(email, password, **extra_fields)


class PlanTier(models.TextChoices):
    FREE = 'free', 'Free'
    HABITS = 'habits', 'Habits'
    SUMMIT = 'summit', 'Summit'
    CLINICAL = 'clinical', 'Clinical'

class User(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    username = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    date_joined = models.DateTimeField(auto_now_add=True)
    auth_provider = models.CharField(max_length=10, choices=AUTH_PROVIDERS, default="BASE")
    thread_id = models.CharField(max_length=100, unique=True, null=True, blank=True)
    # TODO: Remove plan from User model and use UserSubscriptions model instead
    plan = models.CharField(max_length=50, choices=PlanTier.choices, default=PlanTier.FREE)
    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.email
    
    @property

    def has_active_subscription(self):
        return UserSubscription.objects.filter(
        user=self, is_expired=False
        ).exists()

    @property
    def active_subscription(self):
        return UserSubscription.objects.filter(
        user=self, is_expired=False
        ).order_by('-expiry_time').first()
    
    @property
    def subscription_tier(self):
        """
        Returns the tier of the user's active subscription.
        Defaults to the user's plan if no active subscription exists.
        """
        active_subscription = self.active_subscription
        if active_subscription:
            return active_subscription.plan.tier
        return PlanTier.FREE  # Fallback to the user's default plan if no active subscription
        

class Gender(models.TextChoices):
        MALE = 'male', 'Male'
        FEMALE = 'female', 'Female'
        OTHER = 'other', 'Other'

#UserProfile class with a one-to-one relationship with the User model and other feilds , first name , last name , gender , points , badges[List of badges(model)]
class UserProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    full_name = models.CharField(max_length=255, null=True, blank=True)
    points = models.IntegerField(default=0)
    gender = models.CharField(max_length=10, choices=Gender.choices, null=True, blank=True)
    is_onboarding_completed = models.BooleanField(default=False)
    first_tracked_date = models.DateField(null=True, default=None)
    is_profile_completed = models.BooleanField(default=False)
    badges = models.ManyToManyField('Badge', blank=True)
    is_chat_consent = models.BooleanField(default=False)
    birth_date = models.DateField(null=True, default=None)
    profile_image_path = models.CharField(max_length=255, null=True, blank=True)
    time_zone = models.CharField(
        max_length=50,
        choices=[(tz, tz) for tz in pytz.common_timezones],
        default='UTC'
    )
    profile_completion_reminder_date = models.DateTimeField(null=True, default=None)
    streak_reminder_date = models.DateTimeField(null=True, default=None)
    last_daily_nps = models.IntegerField(null=True, default=0)
    last_week_nps = models.IntegerField(null=True, default=0)
    last_month_nps = models.IntegerField(null=True, default=0)
    is_chat_reset=models.BooleanField(default=False)

    inactivity_reminder_date = models.DateTimeField(null=True, default=None)
    
class GoalCategory(models.TextChoices):
    """
    Enum for goal category types.
    """
    NUTRITION = "nutrition", "Nutrition"
    EXERCISE = "exercise", "Exercise"
    UNWIND = "unwind", "Unwind"
    RESTORE = "restore", "Restore"
    OPTIMIZE = "optimize", "Optimize"

class Goal(models.Model):
    """
    Represents a goal that users can select and track.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=GoalCategory.choices)
    max_limit = models.IntegerField(null=True, default=None)
    verb = models.CharField(max_length=50, blank=True, null=True)
    image_url = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.title

class GoalIntensity(models.TextChoices):
    BEGINNER = "Beginner", "BEGINNER"
    ADVANCED = "Advanced", "ADVANCED"

class GoalVersion(models.Model):
    """
    Represents different versions of a goal with varying difficulty levels and check-in limits.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    goal = models.ForeignKey(Goal, on_delete=models.CASCADE, related_name="versions")
    description = models.TextField(blank=True, null=True)
    level = models.CharField(max_length=20, choices=GoalIntensity.choices)

    class Meta:
        constraints = [
            UniqueConstraint(fields=['goal', 'level'], name='unique_goal_intensity')
        ]

    def __str__(self):
        return f"{self.goal.title} - {self.level}"
    
class SelectedGoal(models.Model):
    """
    Represents a goal selected by a user and the current-week related data only
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True) #server time

    start_date = models.DateField(null=True, default=None)
    end_date = models.DateField(null=True, default=None)
    restore_date = models.DateField(null=True, default=None)

    target = models.IntegerField(default=0)
    current_week = models.IntegerField(default=0)
    current_checkins = models.IntegerField(default=0)

    streak = models.IntegerField(default=0)
    streak_awarded = models.BooleanField(default=False)

    goal_version = models.ForeignKey(GoalVersion, on_delete=models.CASCADE)
    goal = models.ForeignKey(Goal, on_delete=models.CASCADE)
    last_tracking_date = models.DateField(null=True, default=None)
    
    is_active = models.BooleanField(default=True)
    restore_week_offset = models.IntegerField(default=0)
    first_tracked_date = models.DateField(null=True, default=None)
    # streak_reminder_date = models.DateTimeField(null=True, default=None)

class GoalTracking(models.Model):
    """
    Tracks the progress of a selected goal on a daily basis.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    selected_goal = models.ForeignKey(SelectedGoal, on_delete=models.CASCADE)
    date = models.DateField()
    goal_version = models.ForeignKey(GoalVersion, on_delete=models.CASCADE)


class BadgeType(models.TextChoices):
    STREAK = 'streak', 'Streak'
    CITY = 'city', 'City'

class BadgeLevel(models.TextChoices):
    BRONZE = 'bronze', 'Bronze'
    SILVER = 'silver', 'Silver'
    GOLD = 'gold', 'Gold'
    SAPPHIRE = 'sapphire', 'Sapphire'
    DIAMOND = 'diamond', 'Diamond'
    PLATINUM = 'platinum', 'Platinum'


class Badge(models.Model):
    """
    Enhanced Badge model with type and level information
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    image_url = models.URLField(null=False)
    badge_type = models.CharField(max_length=10, choices=BadgeType.choices, null=True, blank=True)
    level = models.CharField(max_length=10, choices=BadgeLevel.choices, null=True, blank=True)
    category = models.CharField(max_length=50, choices=GoalCategory.choices, null=True, blank=True)
    city_area = models.CharField(max_length=50, null=True, blank=True)
    required_streak = models.IntegerField(null=True, blank=True)
    required_plots = models.IntegerField(null=True, blank=True)
    required_habits = models.IntegerField(default=1)
    
    class Meta:
        unique_together = [('badge_type', 'level', 'category'), ('badge_type', 'level', 'city_area')]

    def __str__(self):
        level_str = f" ({self.level})" if self.level else ""
        return f"{self.title}{level_str}"

class UserBadge(models.Model):
    """
    Tracks which badges a user has earned and when
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE)
    earned_at = models.DateTimeField(auto_now_add=True, null=True)
    
    class Meta:
        unique_together = ['user', 'badge']

    # def __str__(self):
    #     return f"{self.user.email} - {self.badge.title}"


class Role(models.TextChoices):
        USER = 'user', 'User'
        ASSISTANT = 'assistant', 'Assistant'
        SYSTEM = 'system', 'System'

class NeuroChat(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.TextField(max_length=10000, blank=True, null=True)
    role = models.CharField(max_length=20, choices=Role.choices, null=True, blank=True, default=1)
    date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return self.user.username + ': ' + self.message
    
class UserThread(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    thread_id = models.CharField(max_length=100, unique=True, null=False)
    message_count = models.IntegerField(null=True, blank=True, default=1)

class ActionType(models.TextChoices):
    DAILY_CHECKIN = 'daily_checkin', 'Daily Checkin'
    EXTRA_CHECKIN_BONUS = 'extra_checkin_bonus', 'Extra Checkin Bonus'
    DAILY_APP_OPEN = 'daily_app_open', 'Daily App Open Bonus'
    WEEKLY_STREAK = 'weekly_streak_bonus', 'Weekly Streak Bonus'
    MONTHLY_STREAK = 'monthly_streak_bonus', 'Monthly Streak Bonus'
    THREE_MONTHS_STREAK = 'three_months_streak', 'Three Months Streak Bonus'
    SIX_MONTHS_STREAK = 'six_months_streak', 'Six months streak Bonus'
    ONE_OFF_BIG_ACTIONS = 'one_off_big_actions', 'One Off Big Actions'
    RESTORE_STREAK_COST = 'restore_streak_cost', 'Cost to Restore Streak'
    ASSESMENT_COMPLETION = 'assesment_completion', 'Optimize Arcade: Assesment Game Completion'
    COMMUNITY_ENGAGEMENT_BONUS = 'community_engagement_bonus', 'Community Engagement Bonus'

class Points(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    action = models.CharField(max_length=50, choices=ActionType.choices)
    points = models.IntegerField()

class NotificationType(models.TextChoices):
    WELCOME_ONBOARDING = 'welcome_onboarding'
    PROFILE_COMPLETION = 'profile_completion'
    GOAL_SETTING_ENCOURAGEMENT = 'goal_setting_encouragement'
    CHECK_IN_REMINDER = 'check_in_reminder'
    TARGET_ACHIEVEMENT_CELEBRATION = 'target_achievement_celebration'
    MISSED_STREAK_REMINDER = 'missed_streak_reminder'
    PROGRESS_SUMMARY = 'progress_summary'
    RE_ENGAGEMENT = 're_engagement'
    MORNING_ENGAGEMENTS = 'morning_engagements'
    MIDDAY_SUPPORT = 'midday_support'
    EVENING_WRAP_UPS = 'evening_wrap_ups'
    WEEKLY_ENGAGEMENTS = 'weekly_engagements'
    MONTHLY_ENGAGEMENTS = 'monthly_engagements'

class NotificationSubType(models.TextChoices):
    # Welcome & Onboarding
    WELCOME_TO_NEUROWORLD = 'welcome_to_neuroworld'

    # Profile Completion
    COMPLETE_PROFILE = 'complete_profile'

    # Goal-Setting Encouragement
    START_FIRST_GOAL = 'start_first_goal'

    # Check-In Reminder
    CHECK_IN_REMINDER = 'check_in_reminder'

    # Goal Achievement Celebration / Streak Target Achievement
    GOAL_ACHIEVED = 'goal_achieved'

    # Missed Streak Reminder
    STREAK_BROKEN = 'streak_broken'

    # Progress Summary
    WEEKLY_RECAP = 'weekly_recap'

    # Re-engagement
    WE_MISS_YOU = 'we_miss_you'
    HELP_SEEKING_PROMPT = 'help_seeking_prompt'

    # Morning Engagements
    DAILY_MOTIVATION = 'daily_motivation'
    YOU_GOT_THIS = 'you_got_this'
    CHECK_ENERGY = 'check_energy'
    HEALTH_TIP = 'health_tip'
    SUNNY_DAY = 'sunny_day'

    # Midday Support
    MIDDAY_CHECK = 'midday_check'
    STRETCH_BREAK = 'stretch_break'
    SNACK_TIME = 'snack_time'
    REFOCUS = 'refocus'
    BREATHE = 'breathe'

    # Evening Wrap Ups
    STREAK_REMINDER = 'streak_reminder'
    GREAT_JOB = 'great_job'
    PLAN_TOMORROW = 'plan_tomorrow'
    WIND_DOWN = 'wind_down'
    TIME_TO_SLEEP = 'time_to_sleep'

    # Weekly Engagements
    NEW_WEEK_GOALS = 'new_week_goals'
    MIDWEEK_CHECKIN = 'midweek_checkin'
    REST_REMINDER = 'rest_reminder'

    # Monthly Engagements
    MONTHLY_UPDATE = 'monthly_update'

class Notification(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=50, choices=NotificationType.choices)
    sub_type = models.CharField(max_length=100, choices=NotificationSubType.choices, unique=True)
    title = models.CharField(max_length=255)
    icon = models.CharField(max_length=100)
    message = models.TextField()

    def __str__(self):
        return f"{self.title} - {self.type} ({self.sub_type})"

class UserNotification(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name="user_notifications")
    title = models.CharField(max_length=255)
    body = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    fcm_message_id = models.CharField(max_length=255, null=True) # Store the FCM message ID for tracking delivery status

    class Meta:
        indexes = [
            models.Index(fields=["user", "-timestamp"]), # Index for faster lookups by user and timestamp
        ]
        ordering = ['-timestamp']  # This ensures latest notifications appear on top by default


class UserDevice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    fcm_token = models.CharField(max_length=255)
    updated_at = models.DateTimeField(auto_now=True)

class Feedback(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    comment = models.TextField(blank=True, null=True)
    score = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

class BillingPeriod(models.TextChoices):
    MONTHLY = 'monthly', 'Monthly'
    YEARLY = 'yearly', 'Yearly'
    QUARTERLY = 'quarterly', 'Quarterly'

class SubscriptionPlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tier = models.CharField(max_length=20, choices=PlanTier.choices)
    name = models.CharField(max_length=50)
    billing_period = models.CharField(
        max_length=20,
        choices=BillingPeriod.choices,
        null=True,
        blank=True
    )
    product_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration_days = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.billing_period or 'Free'})"
    
    def feature_list(self):
        return PlanFeature.objects.filter(tier=self.tier)

class SubscriptionPlatform(models.TextChoices):
    GOOGLE = 'google', 'Google Play'
    APPLE = 'apple', 'Apple App Store'

class UserSubscription(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, null=True)
    purchase_token = models.CharField(max_length=512, unique=True)
    status = models.CharField(max_length=100, default='active')  # active, canceled, etc.
    start_time = models.DateTimeField(null=True, blank=True)
    expiry_time = models.DateTimeField(null=True, blank=True)
    is_auto_renewing = models.BooleanField(default=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_cancelled = models.BooleanField(default=False)  # Indicates if the subscription was cancelled by the user
    is_expired = models.BooleanField(default=False)  # Indicates if the subscription has expired
    platform = models.CharField(
        max_length=20,
        choices=SubscriptionPlatform.choices,
        null=True,
        blank=True
    )
    def __str__(self):
        return f"{self.user} - {self.plan}"
    
class PlanFeature(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tier = models.CharField(max_length=20, choices=PlanTier.choices)
    description = models.TextField()
    icon = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"{self.tier} - {self.description}"


class TransactionType(models.TextChoices):
    INITIAL_PURCHASE = 'initial_purchase', 'Initial Purchase'
    RENEWAL = 'renewal', 'Renewal'
    CANCEL = 'cancel', 'Cancel'
    REFUND = 'refund', 'Refund'
    REVOKE = 'revoke', 'Revoke'


class UserTransaction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    subscription = models.ForeignKey(UserSubscription, on_delete=models.CASCADE, null=True, blank=True)
    
    platform = models.CharField(max_length=20, choices=SubscriptionPlatform.choices)

    transaction_id = models.CharField(max_length=255, unique=True)  # Apple: transaction_id, Google: orderId or orderId with suffix
    original_transaction_id = models.CharField(max_length=255, null=True, blank=True)  # Apple: original_transaction_id, Google: purchase_token
    
    product_id = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=10, default="USD")
    
    transaction_type = models.CharField(max_length=50, choices=TransactionType.choices)
    transaction_time = models.DateTimeField()
    
    raw_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    

class Chat(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="chats")
    attrs = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Chat by {self.user.email} at {self.created_at}"